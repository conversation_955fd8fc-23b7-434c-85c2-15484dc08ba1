{"_from": "@types/json-schema@*", "_id": "@types/json-schema@7.0.15", "_inBundle": false, "_integrity": "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==", "_location": "/@types/json-schema", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/json-schema@*", "name": "@types/json-schema", "escapedName": "@types%2fjson-schema", "scope": "@types", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/@types/eslint", "/babel-loader/schema-utils", "/css-minimizer-webpack-plugin/schema-utils", "/mini-css-extract-plugin/schema-utils", "/schema-utils", "/terser-webpack-plugin/schema-utils", "/webpack-dev-middleware/schema-utils"], "_resolved": "https://repo.yyrd.com/artifactory/api/npm/ynpm-all/@types/json-schema/-/json-schema-7.0.15.tgz", "_shasum": "596a1747233694d50f6ad8a7869fcb6f56cf5841", "_spec": "@types/json-schema@*", "_where": "D:\\HOMEWORK\\yonyou\\project\\YUN2SHANG4GUI4ZHOU1\\workspace\\aipoerp\\c-yy-gz-aipoerp-fe\\node_modules\\@types\\eslint", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/bcherny"}, {"name": "<PERSON>", "url": "https://github.com/lucianbuzzo"}, {"name": "<PERSON>", "url": "https://github.com/rolandjitsu"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for json-schema", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/json-schema", "license": "MIT", "main": "", "name": "@types/json-schema", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/json-schema"}, "scripts": {}, "typeScriptVersion": "4.5", "types": "index.d.ts", "typesPublisherContentHash": "79984fd70cd25c3f7d72b84368778c763c89728ea0073832d745d4691b705257", "version": "7.0.15"}
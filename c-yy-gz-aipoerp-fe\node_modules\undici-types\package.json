{"_from": "undici-types@~6.20.0", "_id": "undici-types@6.20.0", "_inBundle": false, "_integrity": "sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg==", "_location": "/undici-types", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "undici-types@~6.20.0", "name": "undici-types", "escapedName": "undici-types", "rawSpec": "~6.20.0", "saveSpec": null, "fetchSpec": "~6.20.0"}, "_requiredBy": ["/@types/node"], "_resolved": "https://repo.yyrd.com/artifactory/api/npm/ynpm-all/undici-types/-/undici-types-6.20.0.tgz", "_shasum": "8171bf22c1f588d1554d55bf204bc624af388433", "_spec": "undici-types@~6.20.0", "_where": "D:\\HOMEWORK\\yonyou\\project\\YUN2SHANG4GUI4ZHOU1\\workspace\\aipoerp\\c-yy-gz-aipoerp-fe\\node_modules\\@types\\node", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/dnlup"}, {"name": "<PERSON>", "url": "https://github.com/ethan-arrowood"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/KhafraDev"}, {"name": "<PERSON>", "url": "https://github.com/ronag"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/szmarczak"}, {"name": "<PERSON>", "url": "https://github.com/delvedor"}], "deprecated": false, "description": "A stand-alone types package for Undici", "files": ["*.d.ts"], "homepage": "https://undici.nodejs.org", "license": "MIT", "name": "undici-types", "repository": {"type": "git", "url": "git+https://github.com/nodejs/undici.git"}, "types": "index.d.ts", "version": "6.20.0"}
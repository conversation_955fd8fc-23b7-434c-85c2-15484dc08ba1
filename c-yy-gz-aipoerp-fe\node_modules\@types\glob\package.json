{"_from": "@types/glob@^7.1.1", "_id": "@types/glob@7.2.0", "_inBundle": false, "_integrity": "sha512-ZUxbzKl0IfJILTS6t7ip5fQQM/J3TJYubDm3nMbgubNNYS62eXeUpoLUC8/7fJNiFYHTrGPQn7hspDUzIHX3UA==", "_location": "/@types/glob", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/glob@^7.1.1", "name": "@types/glob", "escapedName": "@types%2fglob", "scope": "@types", "rawSpec": "^7.1.1", "saveSpec": null, "fetchSpec": "^7.1.1"}, "_requiredBy": ["/del"], "_resolved": "https://repo.yyrd.com/artifactory/api/npm/ynpm-all/@types/glob/-/glob-7.2.0.tgz", "_shasum": "bc1b5bf3aa92f25bd5dd39f35c57361bdce5b2eb", "_spec": "@types/glob@^7.1.1", "_where": "D:\\HOMEWORK\\yonyou\\project\\YUN2SHANG4GUI4ZHOU1\\workspace\\aipoerp\\c-yy-gz-aipoerp-fe\\node_modules\\del", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "vvakame", "url": "https://github.com/vvakame"}, {"name": "voy", "url": "https://github.com/voy"}, {"name": "<PERSON>", "url": "https://github.com/ajafff"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "dependencies": {"@types/minimatch": "*", "@types/node": "*"}, "deprecated": false, "description": "TypeScript definitions for Glob", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/glob", "license": "MIT", "main": "", "name": "@types/glob", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/glob"}, "scripts": {}, "typeScriptVersion": "3.7", "types": "index.d.ts", "typesPublisherContentHash": "436848b740c6ebcf1bfea5b5542b494eb73ed390b43a18c3dffa26ce9bad0aa8", "version": "7.2.0"}
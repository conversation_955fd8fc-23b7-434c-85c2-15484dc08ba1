{"_from": "util@~0.12.0", "_id": "util@0.12.5", "_inBundle": false, "_integrity": "sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==", "_location": "/util", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "util@~0.12.0", "name": "util", "escapedName": "util", "rawSpec": "~0.12.0", "saveSpec": null, "fetchSpec": "~0.12.0"}, "_requiredBy": ["/browserify"], "_resolved": "https://repo.yyrd.com/artifactory/api/npm/ynpm-all/util/-/util-0.12.5.tgz", "_shasum": "5f17a6059b73db61a875668781a1c2b136bd6fbc", "_spec": "util@~0.12.0", "_where": "D:\\HOMEWORK\\yonyou\\project\\YUN2SHANG4GUI4ZHOU1\\workspace\\aipoerp\\c-yy-gz-aipoerp-fe\\node_modules\\browserify", "author": {"name": "<PERSON><PERSON>", "url": "http://www.joyent.com"}, "browser": {"./support/isBuffer.js": "./support/isBufferBrowser.js"}, "bugs": {"url": "https://github.com/browserify/node-util/issues"}, "bundleDependencies": false, "dependencies": {"inherits": "^2.0.3", "is-arguments": "^1.0.4", "is-generator-function": "^1.0.7", "is-typed-array": "^1.1.3", "which-typed-array": "^1.1.2"}, "deprecated": false, "description": "Node.js's util module for all engines", "devDependencies": {"airtap": "~1.0.0", "core-js": "^3.6.5", "is-async-supported": "~1.2.0", "object.assign": "~4.1.0", "object.entries": "^1.1.0", "run-series": "~1.1.4", "safe-buffer": "^5.1.2", "tape": "~4.9.0"}, "files": ["util.js", "support"], "homepage": "https://github.com/browserify/node-util", "keywords": ["util"], "license": "MIT", "main": "./util.js", "name": "util", "repository": {"type": "git", "url": "git://github.com/browserify/node-util.git"}, "scripts": {"test": "node test/node/index.js", "test:browsers": "airtap test/browser/index.js", "test:browsers:local": "npm run test:browsers -- --local", "test:browsers:with-polyfills": "airtap test/browser/with-polyfills.js", "test:browsers:with-polyfills:local": "npm run test:browsers:with-polyfills -- --local"}, "version": "0.12.5"}
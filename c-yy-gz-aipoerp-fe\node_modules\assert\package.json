{"_from": "assert@^1.4.0", "_id": "assert@1.5.1", "_inBundle": false, "_integrity": "sha512-zzw1uCAgLbsKwBfFc8CX78DDg+xZeBksSO3vwVIDDN5i94eOrPsSSyiVhmsSABFDM/OcpE2aagCat9dnWQLG1A==", "_location": "/assert", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "assert@^1.4.0", "name": "assert", "escapedName": "assert", "rawSpec": "^1.4.0", "saveSpec": null, "fetchSpec": "^1.4.0"}, "_requiredBy": ["/browserify"], "_resolved": "https://repo.yyrd.com/artifactory/api/npm/ynpm-all/assert/-/assert-1.5.1.tgz", "_shasum": "038ab248e4ff078e7bc2485ba6e6388466c78f76", "_spec": "assert@^1.4.0", "_where": "D:\\HOMEWORK\\yonyou\\project\\YUN2SHANG4GUI4ZHOU1\\workspace\\aipoerp\\c-yy-gz-aipoerp-fe\\node_modules\\browserify", "bugs": {"url": "https://github.com/browserify/commonjs-assert/issues"}, "bundleDependencies": false, "dependencies": {"object.assign": "^4.1.4", "util": "^0.10.4"}, "deprecated": false, "description": "The node.js assert module, re-packaged for web browsers.", "devDependencies": {"mocha": "~1.21.4", "zuul": "~3.10.0", "zuul-ngrok": "^4.0.0"}, "homepage": "https://github.com/browserify/commonjs-assert", "keywords": ["assert", "browser"], "license": "MIT", "main": "./assert.js", "name": "assert", "repository": {"type": "git", "url": "git://github.com/browserify/commonjs-assert.git"}, "scripts": {"browser-local": "zuul --no-coverage --local 8000 -- test.js", "test": "npm run test-node && npm run test-browser", "test-browser": "zuul -- test.js", "test-native": "TEST_NATIVE=true mocha --ui qunit test.js", "test-node": "mocha --ui qunit test.js"}, "version": "1.5.1"}
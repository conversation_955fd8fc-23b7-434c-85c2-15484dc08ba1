{"_from": "json-schema@0.4.0", "_id": "json-schema@0.4.0", "_inBundle": false, "_integrity": "sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==", "_location": "/json-schema", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "json-schema@0.4.0", "name": "json-schema", "escapedName": "json-schema", "rawSpec": "0.4.0", "saveSpec": null, "fetchSpec": "0.4.0"}, "_requiredBy": ["/jsprim"], "_resolved": "https://repo.yyrd.com/artifactory/api/npm/ynpm-all/json-schema/-/json-schema-0.4.0.tgz", "_shasum": "f7de4cf6efab838ebaeb3236474cbba5a1930ab5", "_spec": "json-schema@0.4.0", "_where": "D:\\HOMEWORK\\yonyou\\project\\YUN2SHANG4GUI4ZHOU1\\workspace\\aipoerp\\c-yy-gz-aipoerp-fe\\node_modules\\jsprim", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/kriszyp/json-schema/issues"}, "bundleDependencies": false, "deprecated": false, "description": "JSON Schema validation and specifications", "devDependencies": {"vows": "*"}, "directories": {"lib": "./lib"}, "files": ["lib"], "homepage": "https://github.com/kriszyp/json-schema#readme", "keywords": ["json", "schema"], "license": "(AFL-2.1 OR BSD-3-Clause)", "main": "./lib/validate.js", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>"}], "name": "json-schema", "repository": {"type": "git", "url": "git+ssh://**************/kriszyp/json-schema.git"}, "scripts": {"test": "vows --spec test/*.js"}, "version": "0.4.0"}
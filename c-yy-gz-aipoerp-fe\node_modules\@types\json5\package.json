{"_from": "@types/json5@^0.0.29", "_id": "@types/json5@0.0.29", "_inBundle": false, "_integrity": "sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==", "_location": "/@types/json5", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/json5@^0.0.29", "name": "@types/json5", "escapedName": "@types%2fjson5", "scope": "@types", "rawSpec": "^0.0.29", "saveSpec": null, "fetchSpec": "^0.0.29"}, "_requiredBy": ["/tsconfig-paths"], "_resolved": "https://repo.yyrd.com/artifactory/api/npm/ynpm-all/@types/json5/-/json5-0.0.29.tgz", "_shasum": "ee28707ae94e11d2b827bcbe5270bcea7f3e71ee", "_spec": "@types/json5@^0.0.29", "_where": "D:\\HOMEWORK\\yonyou\\project\\YUN2SHANG4GUI4ZHOU1\\workspace\\aipoerp\\c-yy-gz-aipoerp-fe\\node_modules\\tsconfig-paths", "author": {"name": "<PERSON>", "email": "https://jasonswearingen.github.io"}, "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for JSON5", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/json5", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "typesPublisherContentHash": "1ed77f2bfd59d290798abf89db281c36565f4a78d97d4e9caab25319d54c6331", "typings": "index.d.ts", "version": "0.0.29"}
{"_from": "punycode@^1.4.1", "_id": "punycode@1.4.1", "_inBundle": false, "_integrity": "sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ==", "_location": "/punycode", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "punycode@^1.4.1", "name": "punycode", "escapedName": "punycode", "rawSpec": "^1.4.1", "saveSpec": null, "fetchSpec": "^1.4.1"}, "_requiredBy": ["/browserify", "/tldjs", "/url"], "_resolved": "https://repo.yyrd.com/artifactory/api/npm/ynpm-all/punycode/-/punycode-1.4.1.tgz", "_shasum": "c0d5a63b2718800ad8e1eb0fa5269c84dd41845e", "_spec": "punycode@^1.4.1", "_where": "D:\\HOMEWORK\\yonyou\\project\\YUN2SHANG4GUI4ZHOU1\\workspace\\aipoerp\\c-yy-gz-aipoerp-fe\\node_modules\\tldjs", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://allyoucanleet.com/"}], "deprecated": false, "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "devDependencies": {"coveralls": "^2.11.4", "grunt": "^0.4.5", "grunt-contrib-uglify": "^0.11.0", "grunt-shell": "^1.1.2", "istanbul": "^0.4.1", "qunit-extras": "^1.4.4", "qunitjs": "~1.11.0", "requirejs": "^2.1.22"}, "files": ["LICENSE-MIT.txt", "punycode.js"], "homepage": "https://mths.be/punycode", "jspm": {"map": {"./punycode.js": {"node": "@node/punycode"}}}, "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "license": "MIT", "main": "punycode.js", "name": "punycode", "repository": {"type": "git", "url": "git+https://github.com/bestiejs/punycode.js.git"}, "scripts": {"test": "node tests/tests.js"}, "version": "1.4.1"}
{"_from": "@types/eslint@*", "_id": "@types/eslint@9.6.1", "_inBundle": false, "_integrity": "sha512-FXx2pKgId/WyYo2jXw63kk7/+TY7u7AziEJxJAnSFzHlqTAS3Ync6SvgYAN/k4/PQpnnVuzoMuVnByKK2qp0ag==", "_location": "/@types/eslint", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/eslint@*", "name": "@types/eslint", "escapedName": "@types%2feslint", "scope": "@types", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/@types/eslint-scope"], "_resolved": "https://repo.yyrd.com/artifactory/api/npm/ynpm-all/@types/eslint/-/eslint-9.6.1.tgz", "_shasum": "d5795ad732ce81715f27f75da913004a56751584", "_spec": "@types/eslint@*", "_where": "D:\\HOMEWORK\\yonyou\\project\\YUN2SHANG4GUI4ZHOU1\\workspace\\aipoerp\\c-yy-gz-aipoerp-fe\\node_modules\\@types\\eslint-scope", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pmdartus"}, {"name": "<PERSON>", "url": "https://github.com/j-f1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/saadq"}, {"name": "<PERSON>", "url": "https://github.com/JasonHK"}, {"name": "<PERSON>", "url": "https://github.com/bradzacher"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin"}, {"name": "<PERSON>", "url": "https://github.com/bmish"}], "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "deprecated": false, "description": "TypeScript definitions for eslint", "exports": {".": {"types": "./index.d.ts"}, "./use-at-your-own-risk": {"types": "./use-at-your-own-risk.d.ts"}, "./rules": {"types": "./rules/index.d.ts"}, "./package.json": "./package.json"}, "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint", "license": "MIT", "main": "", "name": "@types/eslint", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint"}, "scripts": {}, "typeScriptVersion": "4.8", "types": "index.d.ts", "typesPublisherContentHash": "bc2620143f844d291da2d199e7b8e2605e3277f1941a508dc72ac92843b149b6", "version": "9.6.1"}
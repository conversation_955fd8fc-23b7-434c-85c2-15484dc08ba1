{"_from": "@types/minimatch@*", "_id": "@types/minimatch@5.1.2", "_inBundle": false, "_integrity": "sha512-K0VQKziLUWkVKiRVrx4a40iPaxTUefQmjtkQofBkYRcoaaL/8rhwDWww9qWbrgicNOgnpIsMxyNIUM4+n6dUIA==", "_location": "/@types/minimatch", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/minimatch@*", "name": "@types/minimatch", "escapedName": "@types%2fminimatch", "scope": "@types", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/@types/glob"], "_resolved": "https://repo.yyrd.com/artifactory/api/npm/ynpm-all/@types/minimatch/-/minimatch-5.1.2.tgz", "_shasum": "07508b45797cb81ec3f273011b054cd0755eddca", "_spec": "@types/minimatch@*", "_where": "D:\\HOMEWORK\\yonyou\\project\\YUN2SHANG4GUI4ZHOU1\\workspace\\aipoerp\\c-yy-gz-aipoerp-fe\\node_modules\\@types\\glob", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "vvakame", "url": "https://github.com/vvakame"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/shantmarouti"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for minimatch", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/minimatch", "license": "MIT", "main": "", "name": "@types/minimatch", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/minimatch"}, "scripts": {}, "typeScriptVersion": "4.1", "types": "index.d.ts", "typesPublisherContentHash": "266f2226f04264f59fb2aeb3afc253d311ddd99b4ae8534d2e27f8a1379203e4", "version": "5.1.2"}
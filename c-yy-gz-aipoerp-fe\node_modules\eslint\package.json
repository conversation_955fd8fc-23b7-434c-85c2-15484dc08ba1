{"_from": "eslint@^6.8.0", "_id": "eslint@6.8.0", "_inBundle": false, "_integrity": "sha512-K+Iayyo2LtyYhDSYwz5D5QdWw0hCacNzyq1Y821Xna2xSJj7cijoLLYmLxTQgcgZ9mC61nryMy9S7GRbYpI5Ig==", "_location": "/eslint", "_phantomChildren": {"isexe": "2.0.0", "nice-try": "1.0.5", "type-fest": "0.8.1"}, "_requested": {"type": "range", "registry": true, "raw": "eslint@^6.8.0", "name": "eslint", "escapedName": "eslint", "rawSpec": "^6.8.0", "saveSpec": null, "fetchSpec": "^6.8.0"}, "_requiredBy": ["/@mdf/create-app"], "_resolved": "https://repo.yyrd.com/artifactory/api/npm/ynpm-all/eslint/-/eslint-6.8.0.tgz", "_shasum": "62262d6729739f9275723824302fb227c8c93ffb", "_spec": "eslint@^6.8.0", "_where": "D:\\HOMEWORK\\yonyou\\project\\YUN2SHANG4GUI4ZHOU1\\workspace\\aipoerp\\c-yy-gz-aipoerp-fe\\node_modules\\@mdf\\create-app", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bin": {"eslint": "bin/eslint.js"}, "bugs": {"url": "https://github.com/eslint/eslint/issues/"}, "bundleDependencies": false, "dependencies": {"@babel/code-frame": "^7.0.0", "ajv": "^6.10.0", "chalk": "^2.1.0", "cross-spawn": "^6.0.5", "debug": "^4.0.1", "doctrine": "^3.0.0", "eslint-scope": "^5.0.0", "eslint-utils": "^1.4.3", "eslint-visitor-keys": "^1.1.0", "espree": "^6.1.2", "esquery": "^1.0.1", "esutils": "^2.0.2", "file-entry-cache": "^5.0.1", "functional-red-black-tree": "^1.0.1", "glob-parent": "^5.0.0", "globals": "^12.1.0", "ignore": "^4.0.6", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "inquirer": "^7.0.0", "is-glob": "^4.0.0", "js-yaml": "^3.13.1", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.3.0", "lodash": "^4.17.14", "minimatch": "^3.0.4", "mkdirp": "^0.5.1", "natural-compare": "^1.4.0", "optionator": "^0.8.3", "progress": "^2.0.0", "regexpp": "^2.0.1", "semver": "^6.1.2", "strip-ansi": "^5.2.0", "strip-json-comments": "^3.0.1", "table": "^5.2.3", "text-table": "^0.2.0", "v8-compile-cache": "^2.0.3"}, "deprecated": "This version is no longer supported. Please see https://eslint.org/version-support for other options.", "description": "An AST-based pattern checker for JavaScript.", "devDependencies": {"@babel/core": "^7.4.3", "@babel/preset-env": "^7.4.3", "acorn": "^7.1.0", "babel-loader": "^8.0.5", "chai": "^4.0.1", "cheerio": "^0.22.0", "common-tags": "^1.8.0", "core-js": "^3.1.3", "dateformat": "^3.0.3", "ejs": "^2.6.1", "eslint": "file:.", "eslint-config-eslint": "file:packages/eslint-config-eslint", "eslint-plugin-eslint-plugin": "^2.0.1", "eslint-plugin-internal-rules": "file:tools/internal-rules", "eslint-plugin-jsdoc": "^15.9.5", "eslint-plugin-node": "^9.0.0", "eslint-release": "^1.2.0", "eslump": "^2.0.0", "esprima": "^4.0.1", "glob": "^7.1.3", "jsdoc": "^3.5.5", "karma": "^4.0.1", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.3", "karma-webpack": "^4.0.0-rc.6", "leche": "^2.2.3", "lint-staged": "^8.1.5", "load-perf": "^0.2.0", "markdownlint": "^0.15.0", "markdownlint-cli": "^0.17.0", "metro-memory-fs": "^0.54.1", "mocha": "^6.1.2", "mocha-junit-reporter": "^1.23.0", "npm-license": "^0.3.3", "nyc": "^14.1.1", "proxyquire": "^2.0.1", "puppeteer": "^1.18.0", "recast": "^0.18.1", "regenerator-runtime": "^0.13.2", "shelljs": "^0.8.2", "sinon": "^7.3.2", "temp": "^0.9.0", "webpack": "^4.35.0", "webpack-cli": "^3.3.5", "yorkie": "^2.0.0"}, "engines": {"node": "^8.10.0 || ^10.13.0 || >=11.10.1"}, "files": ["LICENSE", "README.md", "bin", "conf", "lib", "messages"], "funding": "https://opencollective.com/eslint", "gitHooks": {"pre-commit": "lint-staged"}, "homepage": "https://eslint.org", "keywords": ["ast", "lint", "javascript", "ecmascript", "espree"], "license": "MIT", "lint-staged": {"*.js": ["eslint --fix", "git add"], "*.md": "markdownlint"}, "main": "./lib/api.js", "name": "eslint", "repository": {"type": "git", "url": "git+https://github.com/eslint/eslint.git"}, "scripts": {"docs": "node Makefile.js docs", "fix": "node Makefile.js lint -- fix", "fuzz": "node Makefile.js fuzz", "generate-alpharelease": "node Makefile.js generatePrerelease -- alpha", "generate-betarelease": "node Makefile.js generatePrerelease -- beta", "generate-rcrelease": "node Makefile.js generatePrerelease -- rc", "generate-release": "node Makefile.js generateRelease", "gensite": "node Makefile.js gensite", "lint": "node Makefile.js lint", "perf": "node Makefile.js perf", "publish-release": "node Makefile.js publishRelease", "test": "node Makefile.js test", "test:cli": "mocha", "webpack": "node Makefile.js webpack"}, "version": "6.8.0"}
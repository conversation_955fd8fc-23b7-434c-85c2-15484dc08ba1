{"_from": "@types/parse-json@^4.0.0", "_id": "@types/parse-json@4.0.2", "_inBundle": false, "_integrity": "sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==", "_location": "/@types/parse-json", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/parse-json@^4.0.0", "name": "@types/parse-json", "escapedName": "@types%2fparse-json", "scope": "@types", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/cosmiconfig"], "_resolved": "https://repo.yyrd.com/artifactory/api/npm/ynpm-all/@types/parse-json/-/parse-json-4.0.2.tgz", "_shasum": "5950e50960793055845e956c427fc2b0d70c5239", "_spec": "@types/parse-json@^4.0.0", "_where": "D:\\HOMEWORK\\yonyou\\project\\YUN2SHANG4GUI4ZHOU1\\workspace\\aipoerp\\c-yy-gz-aipoerp-fe\\node_modules\\cosmiconfig", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "mrmlnc", "url": "https://github.com/mrmlnc"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for parse-json", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/parse-json", "license": "MIT", "main": "", "name": "@types/parse-json", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/parse-json"}, "scripts": {}, "typeScriptVersion": "4.5", "types": "index.d.ts", "typesPublisherContentHash": "d1152b3b9b47f80db7e38510e7a49a297ca3c102317f67bcacb959271c65e6c1", "version": "4.0.2"}
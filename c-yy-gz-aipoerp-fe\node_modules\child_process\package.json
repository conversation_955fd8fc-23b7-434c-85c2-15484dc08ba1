{"_from": "child_process@^1.0.2", "_id": "child_process@1.0.2", "_inBundle": false, "_integrity": "sha512-Wmza/JzL0SiWz7kl6MhIKT5ceIlnFPJX+lwUGj7Clhy5MMldsSoJR0+uvRzOS5Kv45Mq7t1PoE8TsOA9bzvb6g==", "_location": "/child_process", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "child_process@^1.0.2", "name": "child_process", "escapedName": "child_process", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/@mdf/create-app"], "_resolved": "https://repo.yyrd.com/artifactory/api/npm/ynpm-all/child_process/-/child_process-1.0.2.tgz", "_shasum": "b1f7e7fc73d25e7fd1d455adc94e143830182b5a", "_spec": "child_process@^1.0.2", "_where": "D:\\HOMEWORK\\yonyou\\project\\YUN2SHANG4GUI4ZHOU1\\workspace\\aipoerp\\c-yy-gz-aipoerp-fe\\node_modules\\@mdf\\create-app", "author": "", "bugs": {"url": "https://github.com/npm/security-holder/issues"}, "bundleDependencies": false, "deprecated": false, "description": "This package name is not currently in use, but was formerly occupied by another package. To avoid malicious use, npm is hanging on to the package name, but loosely, and we'll probably give it to you if you want it.", "homepage": "https://github.com/npm/security-holder#readme", "keywords": [], "license": "ISC", "main": "index.js", "name": "child_process", "repository": {"type": "git", "url": "git+https://github.com/npm/security-holder.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "1.0.2"}
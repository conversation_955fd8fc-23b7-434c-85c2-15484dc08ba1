{"_from": "@types/eslint-scope@^3.7.3", "_id": "@types/eslint-scope@3.7.7", "_inBundle": false, "_integrity": "sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg==", "_location": "/@types/eslint-scope", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/eslint-scope@^3.7.3", "name": "@types/eslint-scope", "escapedName": "@types%2feslint-scope", "scope": "@types", "rawSpec": "^3.7.3", "saveSpec": null, "fetchSpec": "^3.7.3"}, "_requiredBy": ["/@tinper/next-plugin/webpack", "/webpack"], "_resolved": "https://repo.yyrd.com/artifactory/api/npm/ynpm-all/@types/eslint-scope/-/eslint-scope-3.7.7.tgz", "_shasum": "3108bd5f18b0cdb277c867b3dd449c9ed7079ac5", "_spec": "@types/eslint-scope@^3.7.3", "_where": "D:\\HOMEWORK\\yonyou\\project\\YUN2SHANG4GUI4ZHOU1\\workspace\\aipoerp\\c-yy-gz-aipoerp-fe\\node_modules\\@tinper\\next-plugin\\node_modules\\webpack", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/mysticatea"}], "dependencies": {"@types/eslint": "*", "@types/estree": "*"}, "deprecated": false, "description": "TypeScript definitions for eslint-scope", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/eslint-scope", "license": "MIT", "main": "", "name": "@types/eslint-scope", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/eslint-scope"}, "scripts": {}, "typeScriptVersion": "4.5", "types": "index.d.ts", "typesPublisherContentHash": "49eee35b78c19e2c83bc96ce190c7a88329006f876dd7f1fb378c1e8034fc8f2", "version": "3.7.7"}
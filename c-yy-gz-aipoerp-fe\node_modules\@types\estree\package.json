{"_from": "@types/estree@^1.0.5", "_id": "@types/estree@1.0.6", "_inBundle": false, "_integrity": "sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==", "_location": "/@types/estree", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/estree@^1.0.5", "name": "@types/estree", "escapedName": "@types%2festree", "scope": "@types", "rawSpec": "^1.0.5", "saveSpec": null, "fetchSpec": "^1.0.5"}, "_requiredBy": ["/@tinper/next-plugin/webpack", "/@types/eslint", "/@types/eslint-scope", "/webpack"], "_resolved": "https://repo.yyrd.com/artifactory/api/npm/ynpm-all/@types/estree/-/estree-1.0.6.tgz", "_shasum": "628effeeae2064a1b4e79f78e81d87b7e5fc7b50", "_spec": "@types/estree@^1.0.5", "_where": "D:\\HOMEWORK\\yonyou\\project\\YUN2SHANG4GUI4ZHOU1\\workspace\\aipoerp\\c-yy-gz-aipoerp-fe\\node_modules\\@tinper\\next-plugin\\node_modules\\webpack", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/RReverser"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for estree", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "license": "MIT", "main": "", "name": "@types/estree", "nonNpm": true, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/estree"}, "scripts": {}, "typeScriptVersion": "4.8", "types": "index.d.ts", "typesPublisherContentHash": "0310b41994a6f8d7530af6c53d47d8b227f32925e43718507fdb1178e05006b1", "version": "1.0.6"}
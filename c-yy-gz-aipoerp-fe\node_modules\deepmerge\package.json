{"_from": "deepmerge@^4.2.2", "_id": "deepmerge@4.3.1", "_inBundle": false, "_integrity": "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==", "_location": "/deepmerge", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "deepmerge@^4.2.2", "name": "deepmerge", "escapedName": "deepmerge", "rawSpec": "^4.2.2", "saveSpec": null, "fetchSpec": "^4.2.2"}, "_requiredBy": ["/"], "_resolved": "https://repo.yyrd.com/artifactory/api/npm/ynpm-all/deepmerge/-/deepmerge-4.3.1.tgz", "_shasum": "44b5f2147cd3b00d4b56137685966f26fd25dd4a", "_spec": "deepmerge@^4.2.2", "_where": "D:\\HOMEWORK\\yonyou\\project\\YUN2SHANG4GUI4ZHOU1\\workspace\\aipoerp\\c-yy-gz-aipoerp-fe", "bugs": {"url": "https://github.com/TehShrike/deepmerge/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A library for deep (recursive) merging of Javascript objects", "devDependencies": {"@types/node": "^8.10.54", "is-mergeable-object": "1.1.0", "is-plain-object": "^5.0.0", "jsmd": "^1.0.2", "rollup": "^1.23.1", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "tape": "^4.11.0", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.6.1"}, "engines": {"node": ">=0.10.0"}, "homepage": "https://github.com/TehShrike/deepmerge", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "license": "MIT", "main": "dist/cjs.js", "name": "deepmerge", "repository": {"type": "git", "url": "git://github.com/TehShrike/deepmerge.git"}, "scripts": {"build": "rollup -c", "size": "npm run build && uglifyjs --compress --mangle -- ./dist/umd.js | gzip -c | wc -c", "test": "npm run build && tape test/*.js && jsmd readme.md && npm run test:typescript", "test:typescript": "tsc --noEmit test/typescript.ts && ts-node test/typescript.ts"}, "version": "4.3.1"}